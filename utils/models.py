import os
import re
from typing import Literal

from cachetools import Cache
from langchain.chat_models import init_chat_model
from langchain.chat_models.base import BaseChatModel
from loguru import logger

from utils.env import from_env, secret_from_env

# Create LRU cache with max 1024 entries
_cache = Cache(maxsize=1024)

type ModelAlias = (
    Literal[
        "claude-4-sonnet",
        "claude-3-7-sonnet",
        "claude-3.7-sonnet",
        "claude-3.5-haiku",
        "qwen",
        "gemini-2.5-flash",
        "gemini-2.5-flash-instruct",
        "gemini-2.5-pro",
        "o4-mini-high",
        "gpt-5-high",
        "gpt-5-low",
        "gpt-5-minimal",
        "gpt-5-high-high",
        "gpt-5-high-low",
        "gpt-5-high-minimal",
        "gpt-5-low-high",
        "gpt-5-low-low",
        "gpt-5-low-minimal",
        "gpt-5-minimal-high",
        "gpt-5-minimal-low",
        "gpt-5-minimal-minimal",
        "gpt-5-mini",
        "gpt-5-mini-high",
        "gpt-5-mini-low",
        "gpt-5-mini-minimal",
        "gpt-5-mini-high-high",
        "gpt-5-mini-high-low",
        "gpt-5-mini-high-minimal",
        "gpt-5-mini-low-high",
        "gpt-5-mini-low-low",
        "gpt-5-mini-low-minimal",
        "gpt-5-mini-minimal-high",
        "gpt-5-mini-minimal-low",
        "gpt-5-mini-minimal-minimal",
    ]
    | str
)  # Allow string for dynamic gpt-5 configurations

logged_messages = set()


def _log_once(message: str, level: str = "DEBUG"):
    if message not in logged_messages:
        logged_messages.add(message)
        logger.log(level, message)


def _get_default_thinking_budget(thinking_budget: int | None = None) -> int | None:
    if thinking_budget is not None:
        return int(thinking_budget)
    budget = from_env("LLM_THINKING_BUDGET", default=None)
    return int(budget) if budget else None


def _claude_thinking_value(thinking_budget: int | None = None) -> dict:
    budget_tokens = _get_default_thinking_budget(thinking_budget)
    match budget_tokens:
        case 0:
            return {"type": "disabled"}
        case budget_tokens if budget_tokens < 1024:
            return {"type": "enabled", "budget_tokens": 1024}
        case _:
            return {"type": "enabled", "budget_tokens": budget_tokens}


def _claude_default_thinking_params(thinking_budget: int | None = None) -> dict:
    return {
        "temperature": 1,
        "additional_model_request_fields": {
            "thinking": _claude_thinking_value(thinking_budget),
        },
    }


def _parse_openai_model(model: str, **kwargs) -> str | tuple[str, dict]:
    """parse openai model alias (gpt-5 and o series) to actual model name and config"""
    use_responses_api = kwargs.get("use_responses_api", True)
    reasoning_effort = None
    verbosity = None
    reasoning_summary = "auto" if use_responses_api else None

    # Parse model configuration parameters using regex for better performance
    # Try double parameter first: model-param1-param2
    double_param_pattern = r"^(.+)-(high|medium|low|minimal)-(high|medium|low)$"
    match = re.match(double_param_pattern, model)
    if match:
        model = match.group(1)
        reasoning_effort = match.group(2)
        verbosity = match.group(3)
    else:
        # Try single parameter: model-param1
        single_param_pattern = r"^(.+)-(high|medium|low|minimal)$"
        match = re.match(single_param_pattern, model)
        if match:
            model = match.group(1)
            reasoning_effort = match.group(2)

    # Build response configuration
    if not use_responses_api:
        config = {}
        if reasoning_effort:
            config["reasoning_effort"] = reasoning_effort
        return model, config
    else:
        config = {
            "use_responses_api": use_responses_api,
            "reasoning": {"effort": reasoning_effort, "summary": reasoning_summary},
        }
        if verbosity:
            config["verbosity"] = verbosity
        return model, config


def _parse_external_provider_models(model: str, **kwargs) -> str | tuple[str, dict] | None:
    """parse external provider model prefixes (openrouter, aliyun, etc.)"""
    thinking_budget = kwargs.get("thinking_budget")

    if model.startswith("openrouter:"):
        model_name = model.split(":", 1)[1]
        if model_name.startswith("x-ai/"):
            return model_name, {
                "model_provider": "xai",
                "xai_api_base": "https://openrouter.ai/api/v1",
                "xai_api_key": secret_from_env("OPENROUTER_API_KEY", default=None),
            }
        return model_name, {
            "model_provider": "openai",
            "base_url": "https://openrouter.ai/api/v1",
            "api_key": secret_from_env("OPENROUTER_API_KEY", default=None),
        }
    elif model.startswith("aliyun:"):
        return model.split(":", 1)[1], {
            "model_provider": "openai",
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "api_key": secret_from_env("DASHSCOPE_API_KEY", default=None),
        }
    elif model.startswith("o") or model.startswith("gpt-5"):
        return _parse_openai_model(model, **kwargs)
    elif model.startswith("qwen") and model.endswith("-thinking"):
        return model[:-9], {
            "extra_body": {
                "enable_thinking": True,
                "thinking_budget": _get_default_thinking_budget(thinking_budget),
            }
        }
    else:
        return model


def _parse_model_alias(model: str, **kwargs) -> str | tuple[str, dict]:
    """parse model alias to actual model name"""
    thinking_budget = kwargs.get("thinking_budget")

    match model:
        case "claude-4-sonnet-thinking":
            return "us.anthropic.claude-sonnet-4-20250514-v1:0", _claude_default_thinking_params(thinking_budget)
        case "claude-4-sonnet":
            return "us.anthropic.claude-sonnet-4-20250514-v1:0"
        case "claude-3-7-sonnet" | "claude-3.7-sonnet":
            return "us.anthropic.claude-3-7-sonnet-20250219-v1:0"
        case "claude-3.7-sonnet-thinking":
            return "us.anthropic.claude-3-7-sonnet-20250219-v1:0", _claude_default_thinking_params(thinking_budget)
        case "claude-3.5-haiku":
            return "us.anthropic.claude-3-5-haiku-20241022-v1:0"
        case "qwen":
            return "qwen-max"
        # case "gemini-2.5-flash":
        #     return "gemini-2.5-flash-preview-05-20"
        # case "gemini-2.5-pro":
        #     return "gemini-2.5-pro-preview-06-05"
        # return "gemini-2.5-pro-preview-05-06"
        # return "gemini-2.5-pro-preview-03-25"
        case "gemini-2.5-flash-instant" | "gemini-2.5-flash-instruct":
            return "gemini-2.5-flash", {"thinking_budget": 0, "include_thoughts": False}
        case "gemini-2.5-flash-lite":
            return "gemini-2.5-flash-lite-preview-06-17"
        case _:
            return _parse_external_provider_models(model, **kwargs)


def _parse_model_provider(model: str, /, **kwargs) -> str | tuple[str, dict] | None:
    """parse model to determine the appropriate model provider and its params"""
    if "claude" in model:
        https_proxy = from_env(["HTTPS_PROXY", "BEDROCK_PROXY"], default="")
        _log_once(f"using http proxy: {https_proxy} to access anthropic model {model}")
        from botocore.config import Config

        return "bedrock_converse", {
            "provider": "anthropic",
            # "credentials_profile_name": from_env("AWS_PROFILE", default="inhand"),
            "aws_access_key_id": from_env("AWS_ACCESS_KEY_ID", default=None),
            "aws_secret_access_key": secret_from_env("AWS_SECRET_ACCESS_KEY", default=None),
            "region_name": from_env("AWS_REGION", default="us-west-2"),
            "config": Config(proxies={"https": https_proxy}),
            "base_url": from_env("BEDROCK_BASE_URL", default=None),
        }
    elif model.startswith("qwen") or model.startswith("deepseek"):
        return "openai", {
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "api_key": secret_from_env("DASHSCOPE_API_KEY", default=None),
            "openai_proxy": "",
        }
    elif model.startswith("gemini"):
        thinking_budget = _get_default_thinking_budget(kwargs.get("thinking_budget"))
        model_params = {
            "thinking_budget": thinking_budget,
            "include_thoughts": kwargs.get("include_thoughts", thinking_budget != 0 and thinking_budget is not None),
        }

        api_endpoint = from_env(["GEMINI_API_ENDPOINT", "API_ENDPOINT_PROXY"], default=None)
        if api_endpoint:
            model_params["client_options"] = {
                "api_endpoint": api_endpoint,
            }
        else:
            if grpc_proxy := from_env(["GRPC_PROXY", "GEMINI_PROXY"], default=""):
                _log_once(f"using grpc proxy: {grpc_proxy} to access google model {model}")
                os.environ["grpc_proxy"] = grpc_proxy

        return "google_genai", model_params
    elif model.startswith(("gpt", "o")):
        return "openai"
    else:
        # default to openai for unknown models
        return None


def _init_model_params(model: str, /, **kwargs) -> tuple[str, dict]:
    """initialize model parameters based on model type"""
    match _parse_model_alias(model, **kwargs):
        case (model_name, alias_params):
            model = model_name
            kwargs = alias_params | kwargs
        case model_name:
            model = model_name
    thinking_budget = kwargs.pop("thinking_budget", None)
    if "model_provider" not in kwargs:
        match _parse_model_provider(model, thinking_budget=thinking_budget, **kwargs):
            case (model_provider, provider_params):
                kwargs = provider_params | kwargs
                # the model provider may override the model name
                if "model" in provider_params:
                    model = provider_params["model"]
            case model_provider:
                pass

        kwargs["model_provider"] = model_provider
    else:
        model_provider = kwargs["model_provider"]

    if "max_tokens" not in kwargs:
        kwargs["max_tokens"] = int(from_env("LLM_MAX_TOKENS", default="2048"))

    if _support_temperature(model_provider, model):
        # set default temperature
        if "temperature" not in kwargs:
            kwargs["temperature"] = float(from_env("LLM_TEMPERATURE", default="0"))
    else:
        kwargs.pop("temperature", None)

    return model, kwargs


def _support_temperature(model_provider: str, model: str) -> bool:
    if model_provider == "openai" and (model.startswith("gpt-5") or model.startswith("o")):
        return False
    return True


def init_model(
    model: ModelAlias | str,
    thinking_budget: int | None = None,
    use_responses_api: bool | None = None,
    cache_key: str | None = None,
    **kwargs,
) -> BaseChatModel:
    """
    initialize a chat model with the given parameters

    Args:
        model: model name or alias
        use_responses_api: optional flag to use responses api for reasoning models, used for openai models only, default to False
        thinking_budget: thinking budget in tokens, set to 0 to disable thinking, default to None
        cache_key: optional cache key for model caching, if None, caching is disabled

    Returns:
        BaseChatModel: initialized chat model
    """  # noqa: E501
    if use_responses_api is not None:
        kwargs["use_responses_api"] = use_responses_api
    if thinking_budget is not None:
        kwargs["thinking_budget"] = thinking_budget

    # Check cache if cache_key is provided
    if cache_key is not None:
        try:
            return _cache[cache_key]
        except KeyError:
            pass

    model_name, init_model_params = _init_model_params(model, **kwargs)
    # use depth 1 to print the caller's method name
    logger.opt(depth=1).debug(f"initializing model {model_name} with params {init_model_params}")

    llm = _init_chat_model(model=model_name, **init_model_params)

    # add model name and model to the model object for debugging
    if "_model_name" not in llm:
        llm._model_name = model_name
        llm._model = model

    # Cache the model if cache_key is provided
    if cache_key is not None:
        try:
            _cache[cache_key] = llm
        except ValueError:
            pass

    return llm


def _init_chat_model(model: str, model_provider: str | None, **kwargs) -> BaseChatModel:
    if model_provider == "xai":
        from utils.xai import CustomChatXAI

        return CustomChatXAI(model=model, **kwargs)
    return init_chat_model(model=model, model_provider=model_provider, **kwargs)
