import asyncio
import os
from pathlib import Path
from typing import Optional, TypedDict

import httpx
import yaml
from langchain_core.runnables import RunnableConfig, <PERSON>nableLambda
from langchain_core.tools import tool
from loguru import logger

from utils import Cache, init_model

# 初始化diskcache，设置缓存目录
cache = Cache(
    directory=Path(__file__).parent / ".cache" / "linkedin",
    timeout=30 * 24 * 60 * 60,  # 30d,
    cache_enabled_env_var="LINKEDIN_CACHE_ENABLED",
)


class SearchPeopleItem(TypedDict):
    fullName: str | None
    headline: str | None
    summary: str | None
    profilePicture: str | None
    location: str | None
    profileURL: str | None
    username: str | None


class SearchPeopleResponse(TypedDict):
    data: list[SearchPeopleItem] | None
    total: int | None
    error: str | None


class RapidAPIClient:
    """
    LinkedIn RapidAPI客户端类，封装所有API调用功能
    """

    def __init__(self):
        self._client: httpx.AsyncClient | None = None
        self._semaphore = asyncio.Semaphore(2)

    @property
    def client(self) -> httpx.AsyncClient:
        """
        懒加载创建HTTP客户端
        """
        if self._client is None:
            # check required environment variables
            rapid_api_host = os.environ.get("RAPID_API_HOST")
            rapid_api_key = os.environ.get("RAPID_API_KEY")

            if not rapid_api_host:
                raise ValueError("RAPID_API_HOST environment variable is required")
            if not rapid_api_key:
                raise ValueError("RAPID_API_KEY environment variable is required")

            self._client = httpx.AsyncClient(
                timeout=30.0,
                headers={
                    "x-rapidapi-host": rapid_api_host,
                    "x-rapidapi-key": rapid_api_key,
                },
                base_url=f"https://{rapid_api_host}",
            )
        return self._client

    @cache.memoize()
    async def get_linkedin_profile(self, profile_url: str) -> dict:
        """
        获取单个LinkedIn用户资料
        Args:
            profile_url: LinkedIn个人资料URL
        Returns:
            包含用户资料数据的字典或错误信息
        """
        async with self._semaphore:
            logger.debug(f"send request to fetch LinkedIn profile: {profile_url}")

            # 设置参数
            params = {"username": self.extract_linkedin_username(profile_url)}

            # 发送请求
            response = await self.client.get("/data-connection-count", params=params)

            # 检查请求是否成功
            if response.is_success:
                logger.debug(f"successfully fetch LinkedIn profile: {profile_url}")
                result = response.json()
                if result.get("success", True):
                    data = result.get("data", {})
                    data["connections"] = result.get("connection", 0)
                    data["followers"] = result.get("follower", 0)
                    return self.simplify_linkedin_profile(data)
                else:
                    message = result.get("message", "")
                    logger.warning(f"failed to fetch LinkedIn profile: {profile_url}, {message}")
                    raise Exception(f"fetch LinkedIn profile failed: {profile_url}, {message}")
            else:
                response.raise_for_status()
                return None

    async def search_people(
        self,
        keywords: Optional[str] = None,
        title: Optional[str] = None,
        company: Optional[str] = None,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        **kwargs,
    ) -> SearchPeopleResponse:
        """
        搜索LinkedIn用户
        Args:
            keywords: 关键词
            title: 职位
            company: 公司
            **kwargs: 其他参数
        """
        async with self._semaphore:
            params = {
                "keywords": keywords,
                "keywordsTitle": title,
                "company": company,
                "firstName": first_name,
                "lastName": last_name,
                **kwargs,
            }
            params = {k: v for k, v in params.items() if v is not None}

            logger.debug(f"send request to search LinkedIn people: {params}")
            # 发送请求
            response = await self.client.get("/search-people", params=params)

            # 检查请求是否成功
            if response.status_code == 200:
                logger.debug(f"successfully search LinkedIn people: {params}")
                result = response.json()
                if result.get("success", True):
                    return SearchPeopleResponse(
                        data=result.get("data", {}).get("items", []),
                        total=result.get("data", {}).get("total", 0),
                    )
                else:
                    message = result.get("message", "")
                    logger.warning(f"failed to search LinkedIn people: {keywords}, {message}")
                    return SearchPeopleResponse(error=message)
            else:
                response.raise_for_status()

    @cache.memoize()
    async def get_company_by_domain(self, domain: str) -> dict:
        """
        根据域名获取公司信息
        Args:
            domain: 公司域名，如 apple.com
        Returns:
            包含公司信息的字典或错误信息
        """
        async with self._semaphore:
            logger.debug(f"send request to fetch company by domain: {domain}")

            # 设置参数
            params = {"domain": domain}

            # 发送请求
            response = await self.client.get("/get-company-by-domain", params=params)

            # 检查请求是否成功
            if response.is_success:
                logger.debug(f"successfully fetch company by domain: {domain}")
                result = response.json()
                if result.get("success", True):
                    return self.simplify_company_data(result.get("data", {}))
                else:
                    message = result.get("message", "")
                    logger.warning(f"failed to fetch company by domain: {domain}, {message}")
                    raise Exception(f"fetch company by domain failed: {domain}, {message}")
            else:
                response.raise_for_status()

    @cache.memoize()
    async def get_company_by_username(self, username: str) -> dict:
        """
        根据公司ID获取公司信息
        """
        async with self._semaphore:
            logger.debug(f"send request to fetch company by username: {username}")
            # 发送请求
            response = await self.client.get("/get-company-details", params={"username": username})

            # 检查请求是否成功
            if response.is_success:
                logger.debug(f"successfully fetch company by username: {username}")
                result = response.json()
                if result.get("success", True):
                    return self.simplify_company_data(result.get("data", {}))
                else:
                    message = result.get("message", "")
                    logger.warning(f"failed to fetch company by username: {username}, {message}")
                    raise Exception(f"fetch company by username failed: {username}, {message}")
            else:
                response.raise_for_status()

    @staticmethod
    def extract_linkedin_username(profile_url: str) -> str:
        """
        从LinkedIn个人资料URL中提取用户名

        Args:
            profile_url: LinkedIn个人资料URL，如 https://www.linkedin.com/in/username 或 http://www.linkedin.com/in/username/

        Returns:
            提取出的用户名
        """
        # 处理空URL情况
        if not profile_url:
            logger.info("empty LinkedIn profile URL provided")
            return ""

        # 如果URL包含问号，去掉问号及其后面的内容
        if "?" in profile_url:
            profile_url = profile_url.split("?")[0]

        # 如果URL以斜杠结尾，去掉结尾的斜杠
        if profile_url.endswith("/"):
            profile_url = profile_url[:-1]

        # 提取/in/后面的部分作为用户名
        if "/in/" in profile_url:
            username = profile_url.split("/in/")[-1]
            return username

        # 如果URL格式不符合预期，返回原始URL
        logger.warning(f"could not extract LinkedIn username from URL: {profile_url}")
        return profile_url

    @staticmethod
    def simplify_linkedin_profile(profile_data: dict) -> dict:
        """
        Simplify the LinkedIn profile data to the fields needed for candidate analysis.
        """
        try:
            simplified_data = {
                "name": f"{profile_data.get('firstName', '')} {profile_data.get('lastName', '')}",
                "headline": profile_data.get("headline", ""),
                "location": profile_data.get("geo", {}).get("full", ""),
                "connections": profile_data.get("connections", 0),
                "followers": profile_data.get("followers", 0),
                "summary": profile_data.get("summary", ""),
                "current_position": None,
                "experience": [],
                "education": [],
                "skills": "",
            }

            # extract current position
            if "position" in profile_data and profile_data["position"]:
                current_position = profile_data["position"][0]
                simplified_data["current_position"] = {
                    "title": current_position.get("title", ""),
                    "company": current_position.get("multiLocaleCompanyName", {}).get("en_US", ""),
                    "description": current_position.get("description", ""),
                }

            # extract top skills
            if "skills" in profile_data and profile_data["skills"]:
                simplified_data["skills"] = ", ".join([skill.get("name", "") for skill in profile_data["skills"]])
            return simplified_data
        except Exception as e:
            logger.warning(f"Error simplifying profile data: {str(e)}")
            return profile_data

    @staticmethod
    def simplify_company_data(data: dict) -> dict:
        """
        Simplify the company data to the fields needed for analysis.
        """
        try:
            simplified_data = {
                "id": data.get("id", ""),
                "name": data.get("name", ""),
                "universal_name": data.get("universalName", ""),
                "linkedin_url": data.get("linkedinUrl", ""),
                "description": data.get("description", ""),
                "tagline": data.get("tagline", ""),
                "type": data.get("type", ""),
                "website": data.get("website", ""),
                "phone": data.get("phone", ""),
                "staff_count": data.get("staffCount", 0),
                "staff_count_range": data.get("staffCountRange", ""),
                "follower_count": data.get("followerCount", 0),
                "founded": data.get("founded"),
                "industries": data.get("industries", []),
                "specialities": data.get("specialities", []),
            }

            # extract headquarters
            if "headquarter" in data and data["headquarter"]:
                hq = data["headquarter"]
                simplified_data["headquarters"] = {
                    "city": hq.get("city", ""),
                    "country": hq.get("country", ""),
                    "geographic_area": hq.get("geographicArea", ""),
                    "postal_code": hq.get("postalCode", ""),
                    "line1": hq.get("line1", ""),
                    "line2": hq.get("line2", ""),
                }

            return simplified_data
        except Exception as e:
            logger.warning(f"Error simplifying company data: {str(e)}")
            return data

    async def close(self):
        """
        关闭HTTP客户端连接
        """
        if self._client is not None:
            await self._client.aclose()
            self._client = None

    async def get_company_posts(self, username: str, start: int = 0) -> dict:
        """
        获取公司的LinkedIn posts
        Args:
            username: 公司用户名
            start: 起始位置，默认为0
        Returns:
            包含公司posts数据的字典或错误信息
        """
        async with self._semaphore:
            logger.debug(f"send request to fetch company posts: {username}")

            # 设置参数
            params = {"username": username, "start": start}

            # 发送请求
            response = await self.client.get("/get-company-posts", params=params)

            # 检查请求是否成功
            if response.status_code == 200:
                logger.debug(f"successfully fetch company posts: {username}")
                result = response.json()
                if result.get("success", True):
                    data = result.get("data", [])
                    return {
                        "username": username,
                        "total": result.get("total", 0),
                        "total_page": result.get("totalPage", 0),
                        "pagination_token": result.get("paginationToken", ""),
                        "posts": data.get("posts", []) if isinstance(data, dict) else data,
                    }
                else:
                    message = result.get("message", "")
                    logger.warning(f"failed to fetch company posts: {username}, {message}")
                    return {"username": username, "error": message}
            else:
                response.raise_for_status()


# 全局API客户端实例
rapidapi = RapidAPIClient()


# search people tool for agent
@tool(parse_docstring=True)
async def search_people_tool(
    keywords: str | None = None,
    title: str | None = None,
    company: str | None = None,
    first_name: str | None = None,
    last_name: str | None = None,
) -> str:
    """
    Search for people on LinkedIn using RapidAPI

    Args:
        keywords: search keywords for people names or general terms
        title: job title to filter results
        company: company name to filter results
        first_name: first name to filter results
        last_name: last name to filter results

    Returns:
        Yaml string containing search results with fullName, headline, location, etc.
    """

    try:
        result = await rapidapi.search_people(
            keywords=keywords, title=title, company=company, first_name=first_name, last_name=last_name
        )

        if result.get("error"):
            return f"error: {result['error']}"

        people_data = result.get("data", [])
        if not people_data:
            return "No people found"

        for person in people_data:
            person.pop("profilePicture")

        return yaml.dump(people_data, indent=2, allow_unicode=True)

    except Exception as e:
        logger.error(f"Error in search_people_tool: {str(e)}")
        return f"error: {str(e)}"


@tool(parse_docstring=True)
async def get_linkedin_profiles(profile_urls: list[str]) -> str:
    """
    Retrieve detailed profile information for multiple LinkedIn users in batch.

    Use this function to get comprehensive profile data for several LinkedIn users simultaneously.
    More efficient than individual requests. Ensure all URLs are valid LinkedIn profile URLs.

    Args:
        profile_urls: List of complete LinkedIn profile URLs,
            Must be properly formatted URLs. Invalid URLs may return errors or partial results

    Returns:
        YAML formatted string containing profile information for all users including name,
        position, experience, education, skills, and other publicly accessible LinkedIn data
    """
    get_linkedin_profile = RunnableLambda(rapidapi.get_linkedin_profile)

    results = await get_linkedin_profile.abatch(
        profile_urls, config=RunnableConfig(max_concurrency=2), return_exceptions=True
    )
    return yaml.dump(results, indent=2)


@tool(parse_docstring=True)
async def get_company_details(domains: list[str] | None = None, usernames: list[str] | None = None) -> str:
    """
    Retrieve detailed company information from Apollo.io for multiple companies based on their domain names.

    This function fetches comprehensive company data from Apollo.io including business details, employee count,
    industry classification, revenue information, and contact details for a list of companies. Use this when
    you need to gather business intelligence data about multiple companies simultaneously, such as for
    prospecting, market research, competitive analysis, or lead qualification.

    Args:
        domains: List of company domain names to look up from Apollo.io, e.g. ["apple.com", "google.com", "microsoft.com"].
                Each domain should be a valid, fully-qualified domain name without protocol prefixes (http/https).
                Apollo.io will match these domains to companies in their database and return available information.
        usernames: Optional list of company usernames or alternative identifiers, e.g. ["apple", "google", "microsoft"].
                  If provided, must match the order and length of the domains list. These serve as fallback
                  identifiers when domain matching is ambiguous or to enhance lookup accuracy. Can be None if not available.

    Returns:
        JSON string containing an array of company information objects retrieved from Apollo.io. Each object
        includes available company data such as name, industry, employee count, revenue range, headquarters
        location, founding year, technology stack, and other business intelligence metrics. The structure
        follows Apollo.io's company data schema and can be parsed for integration with CRM systems,
        sales workflows, or business analysis tools."""  # noqa: E501
    results = []

    if domains:
        companies = await RunnableLambda(rapidapi.get_company_by_domain).abatch(
            domains, config=RunnableConfig(max_concurrency=2), return_exceptions=True
        )
        results.extend([c if isinstance(c, dict) else str(c) for c in companies])

    if usernames:
        companies = await RunnableLambda(rapidapi.get_company_by_username).abatch(
            usernames, config=RunnableConfig(max_concurrency=2), return_exceptions=True
        )
        results.extend([c if isinstance(c, dict) else str(c) for c in companies])

    return yaml.dump(results, indent=2)


@tool
async def get_company_posts(username: str = None, summarize: bool = True) -> str:
    """
    Get LinkedIn posts for a company and provide AI summarization analysis.
    Args:
        username: Company username (e.g. "google", "microsoft", "apple")
        summarize: Whether to provide AI summary of posts, default is True
    Returns:
        JSON string containing company posts data, with AI analysis if summarization is enabled
    """  # noqa: E501
    # get company posts with caching
    logger.debug(f"获取公司posts: {username}")
    cache_key = f"posts_{username}"
    result = cache.get(cache_key)

    if result is None:
        result = await rapidapi.get_company_posts(username, 0)
        if "error" not in result:
            # cache for 24 hours
            cache.set(cache_key, result, expire=24 * 60 * 60)
        else:
            logger.warning(f"获取公司posts失败: {result['error']}")
            return yaml.dump(result, indent=2)

    # if summarization is requested, analyze the posts using AI
    if summarize and "posts" in result and result["posts"]:
        try:
            # initialize gpt-4.1-nano model for summarization
            llm = init_model(
                model="gpt-4.1-nano",
                max_tokens=2048,
                temperature=0.5,
            )

            # prepare posts content for analysis
            posts_content = []
            company_name = result.get("username", username)
            for post in result["posts"][:25]:  # limit to top 25 posts
                post_text = post.get("text", "")
                if post_text:
                    posts_content.append(f"Company: {company_name}\nPost: {post_text}\n")

            if posts_content:
                # create summarization prompt
                prompt = f"""Please analyze the following LinkedIn company posts and provide a comprehensive summary:

{chr(10).join(posts_content)}

Please provide:
1. Key themes and topics discussed
2. Company positioning and messaging strategies
3. Industry trends mentioned
4. Notable announcements or updates
5. Others (if any)

Respond in Chinese. Respond without any explanation."""

                summary = await llm.ainvoke(prompt)

                # add summary to result
                return summary.content if hasattr(summary, "content") else str(summary)

        except Exception as e:
            logger.warning(f"posts总结失败: {str(e)}")
            # add error info but continue with raw data
            return f"summary error: {str(object=e)}"

    return yaml.dump(result, indent=2)


if __name__ == "__main__":
    import dotenv

    dotenv.load_dotenv()

    async def main() -> None:
        # test company posts
        posts_data = await get_company_posts.ainvoke(
            input={
                "username": "thinaer",
                "summarize": True,
            }
        )
        print("\nCompany Posts:")
        print(posts_data)
        return

        # test LinkedIn profiles
        # profile_data = await get_linkedin_profiles.ainvoke(
        #     input={
        #         "explanation": "test",
        #         "profile_urls": [
        #             "http://www.linkedin.com/in/username1",
        #             # "http://www.linkedin.com/in/username2/",
        #             # "http://www.linkedin.com/in/username3/",
        #         ],
        #     }
        # )
        # print("LinkedIn Profiles:")
        # print(profile_data)

        # test company by domain
        company_data = await get_company_details.ainvoke(
            input={
                "domains": ["www.thinaer.io"],
                "usernames": ["thinaer"],
            }
        )
        print("\nCompany Data:")
        print(company_data)

    asyncio.run(main())
