import hashlib
import yaml
from pathlib import Path
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
from langgraph.prebuilt import create_react_agent
from loguru import logger
from pydantic import BaseModel, Field

from utils import init_model, Cache

# Import apollo API client
from .tools.apollo import apollo

# Import the search_people_tool from linkedin module
from .tools.linkedin import rapidapi

# Cache for people search results (3-7 days cache for stability)
_cache_dir = Path(__file__).parent / ".cache/people_search"
_cache_dir.mkdir(parents=True, exist_ok=True)

people_search_cache = Cache(
    directory=_cache_dir,
    timeout=5 * 24 * 60 * 60,  # 5-day cache for people search results
)


class SearchPeopleData(BaseModel):
    error: str | None = Field(
        None,
        description="""error message if no matches found or multiple matches found,
- if no matches found, return 'No matches found' or recommend the person you might mean
- if multiple matches found, return 'Multiple matches found' and clarify the information that caused the confusion,
  e.g. found people in different companies and the company names""",
    )

    full_name: str | None = Field(None, description="full name of the person")
    headline: str | None = Field(None, description="headline of the person")
    location: str | None = Field(None, description="location of the person")
    profile_url: str | None = Field(None, description="profile URL of the person")
    summary: str | None = Field(None, description="summary of the person")


# search people tool for agent
@tool(parse_docstring=True)
async def search_people_tool(
    keywords: str | None = None,
    title: str | None = None,
    company: str | None = None,
    first_name: str | None = None,
    last_name: str | None = None,
) -> str:
    """
    Search for people on LinkedIn using RapidAPI

    Args:
        keywords: search keywords for people names or general terms
        title: job title to filter results
        company: company name to filter results
        first_name: first name to filter results
        last_name: last name to filter results

    Returns:
        Yaml string containing search results with fullName, headline, location, etc.
    """

    try:
        result = await rapidapi.search_people(
            keywords=keywords, title=title, company=company, first_name=first_name, last_name=last_name
        )

        if result.get("error"):
            return f"error: {result['error']}"

        people_data = result.get("data", [])
        if not people_data:
            return "No people found"

        for person in people_data:
            person.pop("profilePicture")

        return yaml.dump(people_data, indent=2, allow_unicode=True)

    except Exception as e:
        logger.error(f"Error in search_people_tool: {str(e)}")
        return f"error: {str(e)}"


@tool(parse_docstring=True)
async def get_person_by_email(email: str) -> str:
    """
    Get person information by email address using Apollo API.
    This tool enriches person data by email, revealing personal emails and phone numbers.

    Args:
        email: The email address of the person to search for

    Returns:
        Person details in YAML format including contact information
    """
    logger.info(f"get person by email: {email}")

    try:
        result = await apollo.enrich_person(email=email, reveal_personal_emails=False, reveal_phone_number=False)
    except Exception as e:
        logger.error(f"get person by email error: {str(e)}")
        return f"error: {str(e)}"

    if "error" in result:
        return result["error"]
    else:
        logger.info(f"get person by email success: {result.get('name', 'Unknown')}")
        return f"```yaml\n{yaml.dump(result, indent=2, allow_unicode=True)}\n```"


def _generate_people_search_cache_key(query: str) -> str:
    """Generate cache key for people search based on query content"""
    # Normalize the query to ensure consistent caching
    normalized_query = query.strip().lower()
    # Create hash of the normalized query
    query_hash = hashlib.md5(normalized_query.encode('utf-8')).hexdigest()
    return f"people_search_{query_hash}"


async def agentic_search_people(query: str) -> SearchPeopleData:
    """
    Agentic search people method using LLM and tools with caching for stability.

    Args:
        query: search description in natural language

    Returns:
        SearchPeopleData object containing the person's data
    """
    # Check cache first
    cache_key = _generate_people_search_cache_key(query)
    cached_result = people_search_cache.get(cache_key)

    if cached_result is not None:
        logger.debug(f"Found cached people search result for query: {query[:100]}...")
        return cached_result

    try:
        # create the react agent with search tools
        tools = [search_people_tool, get_person_by_email]

        parser = PydanticOutputParser(pydantic_object=SearchPeopleData)

        # system prompt for people search agent
        system_prompt = """You are a professional LinkedIn people search assistant.
Your goal is to use the search_people_tool to find the right people based on user queries.

## Your Capabilities:
1. Use search_people_tool to search for people on LinkedIn
2. Use get_person_by_email to get person information by email address
3. Perform multi-round searches to find the most relevant people

## Search Strategy:
1. First analyze the user query and plan search strategy
2. Extract key information like:
   - Person names or keywords
   - Email addresses
   - Job titles or roles
   - Company names
   - Location preferences
3. If email address is provided in the query, use get_person_by_email immediately
4. Use search_people_tool with appropriate parameters
5. If initial results are not satisfactory, refine search with different keywords/filters
6. Continue searching until you find relevant people or determine no matches exist
7. Return error message if no matches found or multiple matches found

### Retry search if no matches found
1. User might provide wrong company name, try name with short name, full name, etc.
2. User provided wrong title, try to not specify title as search parameter
3. Try narrow down the params, e.g. remove title, company, location, etc.
4. Use keywords to search
5. Double check the search results to ensure the person is the one you are looking for
6. If still no matches found, return error message

## `search_people_tool` parameters:
- keywords: Use for person names, general search terms, or skills (LinkedIn search)
- title: Use for specific job titles or roles (LinkedIn search)
- company: Use for specific company names (LinkedIn search)
- Other filters as needed for LinkedIn search

## Output Guidelines:
- Return the most relevant people found
- When using get_person_by_email, return the enriched person data as-is
- When using search_people_tool, prioritize people with complete profiles (fullName, headline, location)
- Focus on quality over quantity
- If no good matches found, explain why and suggest alternative approaches

## Important Notes:
- Always use English for search parameters
- Be flexible with search terms - try variations if needed
- Consider synonyms and related terms
- Multiple searches may be needed to find the right people
- Try the best to find the right person, don't give up easily

{format_instructions}
"""

        # create prompt template
        prompt = ChatPromptTemplate.from_messages(
            [
                ("system", system_prompt),
                ("placeholder", "{messages}"),
            ]
        ).partial(format_instructions=parser.get_format_instructions())

        # initialize model
        model = init_model(model="gemini-2.5-flash", temperature=0)

        # create react agent
        agent = create_react_agent(model, tools, prompt=prompt)

        # run the agent
        result = await agent.ainvoke({"messages": [("human", query)]})

        # extract the final result from agent messages
        final_message = result.get("messages", [])[-1]

        search_result = parser.parse(final_message.text())

        # Cache the result for future use (only cache successful results)
        if not search_result.error:
            people_search_cache.set(cache_key, search_result)
            logger.debug(f"Cached people search result for query: {query[:100]}...")

        return search_result
    except Exception as e:
        logger.error(f"Search people error: {str(e)}")
        error_result = SearchPeopleData(error=str(e))
        # Don't cache error results
        return error_result
