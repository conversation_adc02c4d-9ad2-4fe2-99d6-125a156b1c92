from pathlib import Path

from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from langgraph.prebuilt import create_react_agent
from loguru import logger

from agent.tools import (
    firecrawl_scrape,
    tavily_crawl,
    tavily_search,
)
from common.prompts import inhand_business_card
from utils import <PERSON><PERSON>
from utils import safe_dispatch_custom_event as dispatch_custom_event
from utils.models import init_model
from zoho.accounts_api import ZohoAccountInfo

# Primary cache with a longer timeout for stable company info
cache = Cache(
    directory=Path(__file__).parent / ".cache/company_info_scrap",
    timeout=7 * 24 * 60 * 60,  # 7-day cache for stable company info
)

# Fast cache for recent queries (shorter timeout, faster lookup)
recent_cache = Cache(
    directory=Path(__file__).parent / ".cache/company_info_recent",
    timeout=3 * 24 * 60 * 60,  # 2 hours cache for recent/frequent queries
)


def _generate_cache_key(user_query: str | None, zoho_account_info: ZohoAccountInfo) -> str:
    """Generate an optimized cache key based on account ID and query hash"""
    import hashlib

    account_id = zoho_account_info.get("id", "unknown")
    company_name = zoho_account_info.get("name", "unknown")
    industry = zoho_account_info.get("industry", "")

    # Create a hash of the query to handle long/variable queries
    query_hash = hashlib.md5((user_query or "default_query").encode()).hexdigest()[:8]

    # Include industry in a key for better cache segmentation
    cache_key = f"{account_id}_{company_name}_{industry}_{query_hash}"
    return cache_key.replace(" ", "_").replace("/", "_")


async def company_info_scrap(
    user_query: str | None,
    zoho_account_info: ZohoAccountInfo,
    # company_apollo_id: str | None = None,
) -> str:
    """
    抓取公司信息，为后续生成个性化销售邮件提供充分的企业背景和业务洞察。
    使用双层缓存策略：快速缓存用于频繁查询，长期缓存用于稳定数据。

    Args:
        user_query: 用户查询需求
        zoho_account_info: 目标公司在Zoho CRM中的信息
    Returns:
        dict: 公司信息抓取结果
        {
            "name": "公司名称",
            "research_result": "公司信息抓取结果",
            "error": "错误信息",
        }
    """
    try:
        if not zoho_account_info:
            raise ValueError("Account info is required")

        # 校验参数
        user_query = user_query or "scrap latest information about the target company"
        cache_key = _generate_cache_key(user_query, zoho_account_info)

        # Try the recent cache first (faster lookup)
        recent_result = recent_cache.get(cache_key)
        if recent_result is not None and isinstance(recent_result, str):
            logger.debug(f"Found company info in recent cache for key: {cache_key}")
            return recent_result

        # Try main cache
        main_result = cache.get(cache_key)
        if main_result is not None and isinstance(main_result, str):
            logger.debug(f"Found company info in main cache for key: {cache_key}")
            # Store in recent cache for faster future access
            recent_cache.set(cache_key, main_result)
            return main_result

        logger.debug(f"开始抓取公司信息, user_query: {user_query}, company_info: {zoho_account_info}")
        dispatch_custom_event("thinking", "Starting company info scrap...")

        llm = init_model(
            model="gpt-5-low",
            text={"verbosity": "low"},
            max_tokens=4096,
        )

        logger.info(f"Initialized LLM model: {llm}")

        research_tools = [
            tavily_search,
            tavily_crawl,
            firecrawl_scrape,
        ]


        # bind_tools 返回一个新的模型实例，需要赋值
        llm_with_tools = llm.bind_tools(research_tools, tool_choice="any")
        logger.info(f"LLM with tools bound: {llm_with_tools}")

        # 构建提示词和输入
        prompt_path = Path(__file__).parent / "prompts" / "company_info_scrap.md"
        system_prompt = prompt_path.read_text(encoding="utf-8")

        prompt = ChatPromptTemplate.from_messages([("system", system_prompt), ("placeholder", "{messages}")]).partial(
            inhand_business_card=inhand_business_card
        )

        logger.info("Creating react agent...")

        agent = create_react_agent(
            model=llm_with_tools,
            tools=research_tools,
            prompt=prompt,
            name="company_info_scrap_agent",
        )

        logger.info(f"Created agent: {agent}")

        input_message_parts = [f"User Query Requirements:\n```\n{user_query}\n```",
                               "分析用户需求，理解用户抓取什么样的公司信息，根据用户的需求对公司进行分析，并生成公司信息抓取结果。"]

        input_message = "\n\n".join(input_message_parts)

        logger.info(f"Input message: {input_message[:200]}...")
        logger.info("Invoking agent...")

        result = await agent.ainvoke(input={"messages": [("human", input_message)]})

        logger.info(f"Agent result type: {type(result)}")
        logger.info(f"Agent result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")

        # 提取最终结果
        message = result["messages"][-1]
        if isinstance(message, AIMessage) and message.content:
            research_result = message.text()
            dispatch_custom_event("thinking", "company info scrap completed.")

            # Store result in both caches
            cache.set(cache_key, research_result)
            recent_cache.set(cache_key, research_result)
            logger.debug(f"Cached company info with key: {cache_key}")

            return research_result
        else:
            raise Exception("No valid research result")

    except Exception as e:
        error_msg = f"failed to scrap company info: {str(e)}"
        raise Exception(error_msg)
