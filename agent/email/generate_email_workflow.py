import asyncio
from enum import Enum
from pathlib import Path
from pprint import pprint

import yaml
from langchain_core.prompts import ChatPromptTemplate
from langgraph.prebuilt import create_react_agent
from loguru import logger
from pydantic import BaseModel, Field

from agent.email.company_info_scrap import company_info_scrap
from agent.search_people import agentic_search_people
from agent.tools.apollo import apollo
from common.prompts import inhand_business_card
from utils import init_model
from zoho.accounts_api import fetch_accounts_info_by_account_id
from zoho.contacts_api import get_contact_by_id


class WritingStyleEnum(str, Enum):
    FORMAL = "formal"  # 正式风格（Formal / Professional）
    CASUAL = "casual"  # 友好风格（Casual / Friendly）
    CONCISE = "concise"  # 简洁专业（Concise / Direct）
    SALESY = "salesy"  # 销售驱动（Persuasive / Salesy）
    CONSULTATIVE = "consultative"  # 顾问式（Consultative / Value-add）


class OutreachScenariosEnum(str, Enum):
    COLD = "cold"  # 初次冷启动（First Touch / Cold Outreach）
    FOLLOWUP1 = "followup1"  # 首次跟进（Reminder / Follow-up #1）
    FOLLOWUP2 = "followup2"  # 二次/多次跟进（Second+ Follow-up）
    POST_MEETING = "post_meeting"  # 会议后/通话后跟进（Post-meeting / Thank You Email）
    REENGAGEMENT = "reengagement"  # 重新激活（Re-engagement / Break-up）


class EmailTemplate(BaseModel):
    subject: str = Field(..., description="The subject of the email")
    body: str = Field(..., description="The content body of the email in HTML or markdown format")


class AuthorInfo(BaseModel):
    name: str = Field(..., description="The name of the author")
    email: str = Field(..., description="The email address of the author")
    phone: str = Field(..., description="The phone number of the author")


async def get_account_apollo_info(account_id: str) -> dict:
    """获取公司Apollo信息"""
    if not account_id:
        raise ValueError("Cannot found company for contact")

    account_data = await fetch_accounts_info_by_account_id(account_id)
    if not account_data:
        raise ValueError("Company not found")

    apollo_info = await apollo.get_complete_organization_info(
        name=account_data.get("name"),
        location=account_data.get("territory"),
        website_url=account_data.get("website"),
    )
    if "error" in apollo_info:
        raise ValueError("Cannot found company in Apollo")

    return apollo_info


async def get_account_latest_info(contact_info: dict):
    """get latest account information"""
    try:
        logger.info(f"Starting get_account_latest_info with contact_info keys: {list(contact_info.keys())}")
        logger.info(f"contact_info content: {contact_info}")

        # 检查 account_name 字段的结构
        account_name_field = contact_info.get("account_name", {})
        logger.info(f"account_name_field type: {type(account_name_field)}, content: {account_name_field}")

        account_name = contact_info.get("account_name", {}).get("name", "")
        account_id = contact_info.get("account_name", {}).get("id", "")

        logger.info(f"Extracted account_name: {account_name}, account_id: {account_id}")
    except Exception as e:
        logger.error(f"Error in get_account_latest_info initial processing: {e}")
        return {}

    if not account_name or not account_id:
        logger.warning("Missing account_name or account_id, returning empty dict")
        return {}

    # get account info in zoho
    logger.info(f"Fetching account info from Zoho for account_id: {account_id}")
    zoho_account_info = await fetch_accounts_info_by_account_id(account_id)

    if not zoho_account_info:
        logger.warning(f"Cannot found account info in zoho for account_id: {account_id}")
        return {}

    logger.info(
        f"Successfully fetched Zoho account info: {list(zoho_account_info.keys()) if isinstance(zoho_account_info, dict) else type(zoho_account_info)}"
    )

    # 构建针对目标客户公司的查询
    company_name = account_name
    company_industry = zoho_account_info.get("industry", "")

    logger.info(f"Building query for company: {company_name}, industry: {company_industry}")

    # 构建个性化查询，关注客户公司的最新动态和业务信息
    user_query = f"scrap latest information about {company_name}"
    if company_industry:
        user_query += f" in {company_industry} industry"
    user_query += (
        ", focusing on recent business developments, product launches, company news, "
        "and industry trends that could be relevant for InHand Networks' IoT solutions"
    )

    logger.info(f"Generated user query: {user_query}")

    try:
        scrap_account_info = await company_info_scrap(
            user_query=user_query,
            zoho_account_info=zoho_account_info,
        )

        logger.info(f"scrap_account_info: {scrap_account_info}")

        # company_info_scrap 返回字符串，检查是否为有效结果
        if not scrap_account_info:
            logger.warning("Company info scrap returned empty result")
            return {}

        # 返回包含公司信息的字典
        return {
            "company_name": account_name,
            "industry": company_industry,
            "research_result": scrap_account_info,
            "account_id": account_id,
        }

    except Exception as e:
        logger.warning(f"Company info scrap failed: {e}")
        return {}


# workflow:
# 根据 contact_info（zoho 联系人信息， 包含联系人的基本信息，以及公司信息）
# 获取联系人的最新动态
# 根据公司信息，获取公司的最动态
# 整理所有信息
# 信息传入 LLM，针对性的生成个性化邮件
# 如果输入的信息源没有太大的变化，生成的邮件内容需要稳定，而不是每一次都是不同的邮件，


async def generate_email_template(
    contact_info: dict,
    custom_prompt: str | None = None,
    writing_style: WritingStyleEnum | None = None,
    outreach_scenarios: OutreachScenariosEnum | None = None,
    author_info: AuthorInfo | None = None,
) -> EmailTemplate:
    """generate email template for contact
    Args:
        contact_info: basic contact information in zoho
        custom_prompt: user custom prompt
        writing_style: writing style
        outreach_scenarios: outreach scenarios
        author_info: author information

    returns:
        EmailTemplate: generated email template
    """
    # Parallel execution of contact and company information enrichment
    logger.info("Starting parallel information enrichment...")

    # 使用 TaskGroup 确保追踪上下文正确传播（Python 3.11+）
    contact_result = None
    company_result = None

    try:
        async with asyncio.TaskGroup() as tg:
            contact_task = tg.create_task(agentic_search_people(yaml.dump(contact_info, allow_unicode=True)))
            company_task = tg.create_task(get_account_latest_info(contact_info))

        # 获取结果
        contact_result = contact_task.result()
        company_result = company_task.result()

    except* Exception as eg:
        # 处理异常，使用回退值
        logger.warning(f"Some tasks failed: {[str(e) for e in eg.exceptions]}")

        # 检查具体哪个任务失败了
        contact_failed = contact_task.done() and contact_task.exception() is not None
        company_failed = company_task.done() and company_task.exception() is not None

        if contact_result is None or contact_failed:
            logger.warning("Contact enrichment task failed, using original contact_info")
            contact_result = contact_info
        if company_result is None or company_failed:
            logger.warning("Company enrichment task failed, using empty dict")
            company_result = {}

    # Process contact enrichment result
    logger.info(f"contact_result: {contact_result}")
    logger.info(f"company_result: {company_result}")
    if contact_result is None or (hasattr(contact_result, "error") and getattr(contact_result, "error", False)):
        contact_lastest_info = contact_info
    elif hasattr(contact_result, "model_dump") and callable(getattr(contact_result, "model_dump")):
        contact_lastest_info = contact_result.model_dump()  # type: ignore
    else:
        contact_lastest_info = contact_result if contact_result else contact_info

    # Process company enrichment result
    account_latest_info = company_result if isinstance(company_result, dict) else {}

    system_prompt = (Path(__file__).parent / "prompts/generate_email.md").read_text()
    if not system_prompt:
        raise ValueError("Unable to load system prompt file")

    # Build structured input message with clear variable names matching the new prompt
    input_sections = []

    # Core information (always present)
    input_sections.append(f"## account_latest_info\n{account_latest_info}")
    input_sections.append(f"## contact_info\n{contact_lastest_info}")

    logger.info(f"account_latest_info: {account_latest_info}")
    logger.info(f"contact_info: {contact_lastest_info}")

    # Optional parameters
    if author_info:
        input_sections.append(f"## author_info\n{author_info}")

    if writing_style:
        input_sections.append(f"## writing_style\n{writing_style.value}")

    if outreach_scenarios:
        input_sections.append(f"## outreach_scenarios\n{outreach_scenarios.value}")

    if custom_prompt:
        input_sections.append(f"## custom_prompt\n{custom_prompt}")

    input_message = "请基于以下信息生成外联邮件：\n\n" + "\n\n".join(input_sections)

    prompt = ChatPromptTemplate.from_messages([("system", system_prompt), ("placeholder", "{messages}")]).partial(
        inhand_business_card=inhand_business_card,
    )

    llm = init_model(model="gpt-5-low", temperature=0)

    agent = create_react_agent(
        model=llm,
        tools=[],
        prompt=prompt,
        name="generate_email_template",
        response_format=EmailTemplate,
    )

    result = await agent.ainvoke({"messages": [("human", input_message)]})

    if "structured_response" in result and result["structured_response"] is not None:
        email_template: EmailTemplate = result["structured_response"]
        return email_template
    else:
        raise ValueError("Generate email failed")


if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv()

    async def main():
        contact_info = await get_contact_by_id("3091799000100616021")
        if contact_info is None:
            raise ValueError("Contact not found")

        author_info = AuthorInfo(name="jian chen", email="<EMAIL>", phone="+86-xxx-xxxx-xxxx")
        result = await generate_email_template(
            contact_info=contact_info,
            writing_style=WritingStyleEnum.CASUAL,
            outreach_scenarios=OutreachScenariosEnum.COLD,
            author_info=author_info,
        )
        print(f"subject: {pprint(result.subject)}")
        print(f"body: \n{pprint(result.body)}")

    asyncio.run(main())
