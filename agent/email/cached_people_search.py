"""
缓存的联系人搜索模块
为 agentic_search_people 提供缓存包装，确保相同查询的结果稳定性
"""

import hashlib
from pathlib import Path

from loguru import logger

from agent.search_people import SearchPeopleData, agentic_search_people
from utils import Cache

# Cache for people search results (5-day cache for stability)
_cache_dir = Path(__file__).parent / ".cache/people_search"
_cache_dir.mkdir(parents=True, exist_ok=True)

people_search_cache = Cache(
    directory=_cache_dir,
    timeout=5 * 24 * 60 * 60,  # 5-day cache for people search results
)


def _generate_people_search_cache_key(query: str) -> str:
    """Generate cache key for people search based on query content"""
    # Normalize the query to ensure consistent caching
    normalized_query = query.strip().lower()
    # Create hash of the normalized query
    query_hash = hashlib.md5(normalized_query.encode('utf-8')).hexdigest()
    return f"people_search_{query_hash}"


async def cached_agentic_search_people(query: str) -> SearchPeopleData:
    """
    Cached wrapper for agentic_search_people to ensure result stability.
    
    Args:
        query: search description in natural language
        
    Returns:
        SearchPeopleData object containing the person's data
    """
    # Check cache first
    cache_key = _generate_people_search_cache_key(query)
    cached_result = people_search_cache.get(cache_key)
    
    if cached_result is not None and isinstance(cached_result, SearchPeopleData):
        logger.debug(f"Found cached people search result for query: {query[:100]}...")
        return cached_result
    
    try:
        # Call the original function
        result = await agentic_search_people(query)
        
        # Cache the result for future use (only cache successful results)
        if not result.error:
            people_search_cache.set(cache_key, result)
            logger.debug(f"Cached people search result for query: {query[:100]}...")
        
        return result
        
    except Exception as e:
        logger.error(f"Cached people search error: {str(e)}")
        return SearchPeopleData(error=str(e))
