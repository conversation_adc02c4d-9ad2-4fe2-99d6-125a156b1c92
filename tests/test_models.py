import asyncio
import os
from unittest.mock import patch

import pytest
from langchain_aws import ChatBedrockConverse
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI

from utils.logging_callback_handler import logging_callback
from utils.messages import message_content
from utils.models import _get_default_thinking_budget, init_model


class TestGetDefaultThinkingBudget:
    """test cases for _get_default_thinking_budget function"""

    def test_get_default_thinking_budget_with_valid_integer(self):
        """test with valid integer environment variable"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": "1024"}):
            result = _get_default_thinking_budget()
            assert result == 1024

    def test_get_default_thinking_budget_with_zero(self):
        """test with zero value environment variable"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": "0"}):
            result = _get_default_thinking_budget()
            assert result == 0

    def test_get_default_thinking_budget_with_negative_value(self):
        """test with negative value environment variable"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": "-100"}):
            result = _get_default_thinking_budget()
            assert result == -100

    def test_get_default_thinking_budget_with_large_value(self):
        """test with large integer value"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": "999999"}):
            result = _get_default_thinking_budget()
            assert result == 999999

    def test_get_default_thinking_budget_without_env_var(self):
        """test when environment variable is not set"""
        with patch.dict(os.environ, {}, clear=True):
            result = _get_default_thinking_budget()
            assert result is None

    def test_get_default_thinking_budget_with_empty_string(self):
        """test with empty string environment variable"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": ""}):
            result = _get_default_thinking_budget()
            assert result is None

    def test_get_default_thinking_budget_with_invalid_string(self):
        """test with invalid string that cannot be converted to int"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": "invalid"}):
            with pytest.raises(ValueError):
                _get_default_thinking_budget()

    def test_get_default_thinking_budget_with_float_string(self):
        """test with float string that cannot be converted to int directly"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": "123.45"}):
            with pytest.raises(ValueError):
                _get_default_thinking_budget()

    def test_get_default_thinking_budget_with_whitespace(self):
        """test with whitespace in environment variable"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": "  1024  "}):
            result = _get_default_thinking_budget()
            assert result == 1024

    def test_get_default_thinking_budget_with_scientific_notation(self):
        """test with scientific notation string"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": "1e3"}):
            with pytest.raises(ValueError):
                _get_default_thinking_budget()


async def test_model_stream_message():
    from langfuse.langchain import CallbackHandler

    model = init_model(
        "o4-mini-low",
        use_responses_api=True,
        # extra_body={"enable_thinking": True, "thinking_budget": 64},
    )

    async for chunk in model.astream("写一个笑话", config={"callbacks": [CallbackHandler()]}):
        print(message_content(chunk), end="", flush=True)


async def test_gemini_flash_instant():
    model = init_model(
        "gemini-2.5-flash-instant",
    )
    result = await asyncio.wait_for(
        model.ainvoke("写一个笑话"),
        timeout=10,
    )
    assert "<think>" not in message_content(result)


async def test_model_message():
    model = init_model(
        "o4-mini-low",
        use_responses_api=True,
    )
    assert isinstance(model, ChatOpenAI)
    result = await model.ainvoke("写一个笑话")
    print(message_content(result))


async def test_gemini_model():
    model = init_model(
        "gemini-2.5-flash-lite",
        thinking_budget=512,
        client_options={
            "api_endpoint": "apiproxy.inhand.ai",
        },
    )
    assert isinstance(model, ChatGoogleGenerativeAI)
    with logging_callback():
        result = await model.ainvoke("写一个笑话，并在最后输出你的模型")
        assert "<think>" in message_content(result)


async def test_openai_model():
    model = init_model("gpt-4.1-mini", base_url="https://apiproxy.inhand.ai/openai/v1")
    with logging_callback():
        result = await model.ainvoke("写一个笑话，并在最后输出你的模型")
        assert result is not None


async def test_openrouter_model():
    model = init_model("openrouter:moonshotai/kimi-k2")
    with logging_callback():
        await model.ainvoke("写一个笑话")


async def test_aliyun_kimi_k2():
    model = init_model("aliyun:Moonshot-Kimi-K2-Instruct")
    with logging_callback():
        await model.ainvoke("写一个笑话")


async def test_claude_model():
    from utils.agent import print_stream

    with patch.dict(os.environ, {"BEDROCK_BASE_URL": "", "BEDROCK_PROXY": "http://localhost:1087"}):
        model = init_model(
            "claude-3.5-haiku",
            thinking_budget=64,
        )

        assert isinstance(model, ChatBedrockConverse)
        await print_stream(model.astream("写一个冷笑话"))


async def test_claude_model_with_base_url():
    from utils.agent import print_stream

    with patch.dict(os.environ, {"BEDROCK_BASE_URL": "https://apiproxy.inhand.ai"}):
        model = init_model(
            "claude-3.5-haiku",
            thinking_budget=64,
        )

        assert isinstance(model, ChatBedrockConverse)
        await print_stream(model.astream("写一个冷笑话"))


async def test_disable_thinking():
    model = init_model(
        "gemini-2.5-flash-lite",
        thinking_budget=0,
    )
    result = await model.ainvoke("写一个冷笑话")
    print(message_content(result))

    # message content should not contains <think> tag
    assert "<think>" not in message_content(result)


async def test_openai_gpt_5_model():
    model = init_model("gpt-5")
    with logging_callback():
        result = await model.ainvoke("写一个笑话")
        assert result is not None


async def test_openai_gpt_5_mini():
    model = init_model("gpt-5-mini")
    with logging_callback():
        result = await model.ainvoke("写一个笑话")
        assert result is not None


async def test_openai_gpt_5_low():
    model = init_model("gpt-5-mini-low")
    with logging_callback():
        result = await model.ainvoke("写一个笑话")
        assert result is not None


async def test_openai_gpt_5_minimal():
    model = init_model("gpt-5-mini-minimal")
    with logging_callback():
        result = await model.ainvoke("写一个笑话")
        assert result is not None


async def test_openrouter_grok_code_fast_1():
    model = init_model("openrouter:x-ai/grok-code-fast-1")
    with logging_callback():
        result = await model.ainvoke("编写一道小学奥数题，并给出答案")
        assert result is not None


def test_model_cache_with_same_cache_key(self):
    """Test that models with same cache_key are reused from cache"""
    # Clear the cache first
    from utils.models import _cache

    _cache.clear()

    # Create first model with cache_key
    model1 = init_model("gpt-4.1-mini", cache_key="test_model")
    model1_id = id(model1)

    # Create second model with same cache_key
    model2 = init_model("gpt-4.1-mini", cache_key="test_model")
    model2_id = id(model2)

    # They should be the same object (cached)
    assert model1_id == model2_id
    assert model1 is model2


def test_model_cache_with_different_cache_key(self):
    """Test that models with different cache_keys create separate instances"""
    # Clear the cache first
    from utils.models import _cache

    _cache.clear()

    # Create models with different cache_keys
    model1 = init_model("gpt-4.1-mini", cache_key="test_model_1")
    model2 = init_model("gpt-4.1-mini", cache_key="test_model_2")

    # They should be different objects
    assert id(model1) != id(model2)
    assert model1 is not model2


def test_model_cache_without_cache_key(self):
    """Test that models without cache_key are not cached"""
    # Clear the cache first
    from utils.models import _cache

    _cache.clear()

    # Create models without cache_key
    model1 = init_model("gpt-4.1-mini")
    model2 = init_model("gpt-4.1-mini")

    # They should be different objects (not cached)
    assert id(model1) != id(model2)
    assert model1 is not model2


def test_model_cache_key_with_different_params(self):
    """Test that different parameters with same cache_key still reuse cached model"""
    # Clear the cache first
    from utils.models import _cache

    _cache.clear()

    # Create first model with cache_key and some params
    model1 = init_model("gpt-4.1-mini", temperature=0.5, cache_key="test_model")

    # Create second model with same cache_key but different params
    # Note: The cache_key parameter is what matters for caching, not other params
    model2 = init_model("gpt-4.1-mini", temperature=0.8, cache_key="test_model")

    # They should be the same object (cached based on cache_key)
    assert id(model1) == id(model2)
    assert model1 is model2
