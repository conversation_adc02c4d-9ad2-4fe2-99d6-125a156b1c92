import pprint

from loguru import logger

from agent.search_people import agentic_search_people, get_person_by_email
from agent.tools.linkedin import rapidapi
from utils.logging_callback_handler import logging_callback


async def test_search_people_api():
    result = await rapidapi.search_people(keywords="<PERSON> Aureyre", title="R&D CTO Office")
    print(result)
    data = result.get("data")
    assert data is not None
    assert result.get("error") is None
    assert len(data) > 0
    assert data[0].get("fullName") is not None
    assert data[0].get("headline") is not None
    assert data[0].get("summary") is not None
    assert data[0].get("profilePicture") is not None
    assert data[0].get("location") is not None
    assert data[0].get("profileURL") is not None


async def test_search_people_tmp():
    result = await rapidapi.search_people(first_name="<PERSON>", last_name="<PERSON><PERSON><PERSON>", company="verizon")
    logger.info(pprint.pformat(result))
    logger.info(f"found {len(result.get('data', []))} people")
    data = result.get("data")
    assert data is not None
    assert result.get("error") is None
    assert len(data) > 0


async def test_agentic_search_people():
    with logging_callback():
        result = await agentic_search_people("Laurent Aureyre @ Schindler")
        print(result)
        assert result is not None
        assert result.profile_url == "https://www.linkedin.com/in/laurent-aureyre"


async def test_agentic_search_people_by_email():
    with logging_callback():
        result = await agentic_search_people("Alexander Tobias, <EMAIL>, product manager")
        print(result)
        assert result is not None
        assert result.profile_url == "http://www.linkedin.com/in/alexander-tobiasen-1a2a633"


async def test_agentic_search_people_multi_results():
    with logging_callback():
        result = await agentic_search_people("Harley")
        print(result)
        assert result.error is not None
        assert result.profile_url is None


async def test_agentic_search_people_no_results():
    with logging_callback():
        result = await agentic_search_people("Someone don't exist")
        print(result)
        assert result.error is not None
        assert result.profile_url is None


async def test_agentc_search_people_wrong_query():
    with logging_callback():
        result = await agentic_search_people("Laurent Aureyre @ InHand Networks")
        print(result)
        assert result.error is not None


async def test_agentic_search_people_wrong_company_name():
    with logging_callback():
        result = await agentic_search_people("""
account_name:
  id: '3091799000034279064'
  name: Verizon Wireless HQ
created_by:
  email: <EMAIL>
  id: '3091799000000144015'
  name: Ming Li
currency: USD
first_name: Vijay
full_name: Vijay Paulrajan
id: '3091799000211603045'
last_name: Paulrajan
mailing_city: Basking Ridge
mailing_country: United States
mailing_state: NJ
mailing_street: 1 Verizon Way
mailing_zip: 07920
owner:
  email: <EMAIL>
  id: '3091799000000144015'
  name: Ming Li
title: VP Business Devices
""")
        print(result)
        assert result.profile_url == "https://www.linkedin.com/in/vijaypaulrajan"


async def test_agentic_search_people_wrong_company_name_2():
    with logging_callback():
        result = await agentic_search_people("Find people at Verizon Wireless HQ who match: David Bien")
        print(result)
        assert result.profile_url == "https://www.linkedin.com/in/david-bien-b68a4896"


async def test_agentic_search_people_wrong_title():
    with logging_callback():
        result = await agentic_search_people("Vijay Paulrajan at Verizon Wireless HQ, VP Business Devices")
        print(result)
        assert result.profile_url == "https://www.linkedin.com/in/vijaypaulrajan"


async def test_get_person_by_email():
    """Test the new get_person_by_email tool"""
    result = await get_person_by_email.coroutine("<EMAIL>")
    print("get_person_by_email result:", result)
    # The result should be either a YAML string or an error message
    assert isinstance(result, str)
    assert len(result) > 0
