"""
公共类型和枚举定义模块
用于避免循环导入
"""

from enum import Enum
from typing import Any, Dict, List, Optional, TypedDict

from pydantic import BaseModel, Field
from typing_extensions import NotRequired


class TaskType(str, Enum):
    """任务类型枚举"""

    DETECT_CONTACTS = "detect_contacts"  # 探查联系人
    LOOKALIKE_COMPANIES = "lookalike_companies"  # 找相似公司


class TaskStatus(str, Enum):
    """任务状态枚举"""

    PENDING = "pending"  # 等待执行
    RUNNING = "running"  # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"  # 执行失败
    CANCELED = "canceled"  # 被取消
    SUCCESS = "success"  # 成功


class MessageType(str, Enum):
    THINKING = "thinking"  # 思考中
    START = "start"  # 开始
    TOTAL = "total"  # 总人数
    LATENT = "latent"  # 潜在联系人
    FILTER_LATENT = "filter_latent"  # 过滤潜在联系人
    GET_CONTACTS_INFO = "get_contacts_info"  # 获取联系人信息
    UPDATE_CONTACTS_INFO = "update_contacts_info"  # 更新联系人信息
    GET_COMPLETED_CONTACTS_DATA = "get_completed_contacts_data"  # 获取完成的数据
    READY_DATA_TRANSFORM = "ready_data_transform"  # 准备转换数据格式
    DATA_TRANSFORM = "data_transform"  # 数据转换
    ADD_CONTACTS = "add_contacts"  # 添加联系人
    COMPLETED = "completed"  # 完成
    SUCCESS = "success"  # 成功
    ERROR = "error"  # 错误


class CustomPrompts(BaseModel):
    """自定义提示词模型"""

    prompt: str = Field(..., description="提示内容")
    cover_system_prompt: bool = False


class CurrentUser(BaseModel):
    """当前用户信息模型"""

    email: Optional[str] = Field(None, description="用户邮箱")
    username: Optional[str] = Field(None, description="用户名")
    user_id: Optional[str] = Field(None, description="用户ID")


class CompanyInfo(BaseModel):
    """公司信息模型"""

    account_id: str = Field(..., description="账户ID")
    owner_id: Optional[str] = Field(None, description="所有者 ID")
    custom_prompts: Optional[CustomPrompts] = None
    current_user: Optional[CurrentUser] = None
    request_headers: Optional[dict[str, Any]] = None


class BatchAccountTaskRequest(BaseModel):
    """批量账户任务请求模型"""

    account_ids: List[str] = Field(..., description="账户ID列表", min_length=1)


class ResponseData(TypedDict):
    type: MessageType
    total: NotRequired[int]
    data: NotRequired[List[Dict[str, Any]]]
    message: str


class ContactEnrichmentStatus(TypedDict):
    contact_id: str
    request_id: str
    status: str  # pending, success, failed
    updated_at: str
