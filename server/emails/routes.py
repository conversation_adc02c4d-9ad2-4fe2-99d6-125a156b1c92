import asyncio
from typing import <PERSON><PERSON><PERSON>enerator

from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from loguru import logger
from pydantic import BaseModel, Field

from agent.email.generate_email_workflow import (
    OutreachScenariosEnum,
    WritingStyleEnum,
    generate_email_template,
)
from server.common_types import CurrentUser
from server.streaming import SseMessageStream, StreamMessage
from zoho.accounts_api import get_owner_info_by_user_id
from zoho.contacts_api import get_contact_by_id

router = APIRouter(tags=["emails"])


class GenerateEmailRequest(BaseModel):
    custom_prompt: str | None = Field(None, description="The custom prompt of email")
    writing_style: WritingStyleEnum | None = Field(None, description="The writing style of email")
    outreach_scenarios: OutreachScenariosEnum | None = Field(None, description="The outreach scenarios of email")
    user_info: CurrentUser | None = Field(None, description="Current user information")


async def _handle_generate_email_flow(
    contact_id: str, request: GenerateEmailRequest
) -> AsyncGenerator[StreamMessage, None]:
    """Handle the complete email generation flow and stream messages for each step."""
    try:
        user_info = request.user_info

        # Input validation
        if not contact_id:
            yield StreamMessage.from_exception(Exception("Contact ID is required"))
            return

        yield StreamMessage.from_step_start("email_generation")

        try:
            # Step 1: Parallel fetch contact and user information
            yield StreamMessage.from_thinking("Fetching contact and user information in parallel...")

            # Create parallel tasks for independent API calls
            contact_task = get_contact_by_id(contact_id)
            user_task = get_owner_info_by_user_id(user_info.user_id) if user_info and user_info.user_id else None

            # Execute tasks in parallel
            if user_task:
                contact_info, zoho_user_info = await asyncio.gather(contact_task, user_task)
            else:
                contact_info = await contact_task
                zoho_user_info = user_info.model_dump() if user_info else None

            # Validate contact information
            if not contact_info:
                yield StreamMessage.from_exception(Exception("Contact not found"))
                return

            # Check if a contact has email
            if not contact_info.get("email"):
                yield StreamMessage.from_exception(Exception("Contact must have email"))
                return

            contact_name = contact_info.get("full_name", "Unknown Contact")
            company_name = contact_info.get("account_name", {}).get("name", "Unknown Company")

            yield StreamMessage.from_thinking(f"Found contact: {contact_name} from {company_name}")

            logger.info(f"Found user: {zoho_user_info}")

            # Convert to AuthorInfo format
            from agent.email.generate_email_workflow import AuthorInfo

            author_info = None
            if zoho_user_info:
                author_name = (
                    f"{zoho_user_info.get('first_name', '')} {zoho_user_info.get('last_name', '')}"
                ).strip()
                author_info = AuthorInfo(
                    name=author_name,
                    email=zoho_user_info.get("email", ""),
                    phone=zoho_user_info.get("phone", "") or zoho_user_info.get("mobile", "") or "",
                )
                yield StreamMessage.from_thinking(f"Email will be sent from: {author_name}")
            else:
                yield StreamMessage.from_thinking("Using default sender information")

            # Step 2: Generate email template (with integrated Langfuse tracking)
            yield StreamMessage.from_thinking("Generating personalized email template...")

            email_template = await generate_email_template(
                contact_info=contact_info,
                custom_prompt=request.custom_prompt,
                writing_style=request.writing_style,
                outreach_scenarios=request.outreach_scenarios,
                author_info=author_info,
                user_info=user_info.model_dump() if user_info else None,
            )

            yield StreamMessage.from_thinking("Email template generated successfully!")

            # Step 3: Send a completion message with structured data
            result_data = {
                "subject": email_template.subject,
                "content": email_template.body,
                "contact_name": contact_name,
                "company_name": company_name,
            }

            yield StreamMessage.from_data(
                data=result_data,
            )

        except ValueError as e:
            error_msg = str(e)
            logger.error(f"Validation error for contact {contact_id}: {error_msg}")
            yield StreamMessage.from_exception(Exception(error_msg))
            return

        except Exception as e:
            error_msg = f"Failed to generate email template: {str(e)}"
            logger.error(f"Generate email template for contact {contact_id} failed: {e}", exc_info=True)
            yield StreamMessage.from_exception(Exception(error_msg))
            return

    except asyncio.CancelledError:
        logger.info("Email generation stream cancelled")
        return
    except Exception as e:
        logger.error(f"Email generation stream error: {e}", exc_info=True)
        yield StreamMessage.from_exception(Exception(str(e)))


@router.post("/emails/contact/{contact_id}/generate")
async def generate_email(contact_id: str, request: GenerateEmailRequest):
    """generate email template for contact"""
    return StreamingResponse(
        content=SseMessageStream(_handle_generate_email_flow(contact_id, request)),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
        },
    )
