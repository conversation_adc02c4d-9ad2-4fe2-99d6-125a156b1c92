from pprint import pprint

import yaml
from fastapi import APIRouter, HTTPException
from langfuse import get_client
from loguru import logger
from pydantic import BaseModel, Field

from agent.search_people import agentic_search_people
from agent.tools.apollo import apollo
from utils.extractors import extract_structure_response
from zoho.browser_api import ZohoBrowserAPI
from zoho.contacts_api import get_contact_by_id
from zoho.models import ZohoContactData

router = APIRouter(tags=["contacts"])


# 手机号的信息，直接更新
# 除了手机号的信息，返回结果，选择更新


@router.get("/contact/{contact_id}/enrich")
async def enrich_contact_info(contact_id: str, username: str = None, email: str = None):
    """
    根据 contact_id 获取联系人扩充信息
    """

    # 从zoho中获取联系人信息
    contact_zoho_info = await get_contact_by_id(contact_id)

    if contact_zoho_info is None:
        # 联系人不存在
        raise HTTPException(status_code=400, detail={"error": "not_found", "messages": "Contact not found"})

    # 获取接口传入的参数：user_info
    langfuse_client = get_client()
    with langfuse_client.start_as_current_span(
        name="enrich_contact_info",
        input=f"Contact Name: {contact_zoho_info.get('full_name', '')} , Contact ID: {contact_id}",
    ) as langfuse_trace_span:
        langfuse_client.update_current_trace(
            user_id=email,
            metadata={"contact_id": contact_id, "user_info": {"username": username, "email": email}},
        )
        # remove unimportant interference information such as owner and created_by
        contact_zoho_info.pop("owner")
        contact_zoho_info.pop("created_by")

        linkedin_url = contact_zoho_info.get("linkedIn")
        if not linkedin_url:
            # if not linkedin_url, search by name from LinkedIn
            searched_person = await agentic_search_people(yaml.dump(contact_zoho_info, allow_unicode=True))

            if searched_person.error or not searched_person.profile_url:
                langfuse_trace_span.update(output={"error": searched_person.error})
                raise HTTPException(
                    status_code=400,
                    detail={"error": "failed", "messages": "Contact not found in LinkedIn"},
                )

            logger.info(f"searched person from LinkedIn: {searched_person}")
            linkedin_url = searched_person.profile_url

        reveal_phone_number = False if contact_zoho_info.get("phone") or contact_zoho_info.get("mobile") else True

        person = await apollo.enrich_person(
            linkedin_url=linkedin_url,
            reveal_phone_number=reveal_phone_number,
        )

        if not person:
            langfuse_trace_span.update(output={"error": "Failed to enrich contact"})
            # 扩充请求失败
            raise HTTPException(
                status_code=400,
                detail={"error": "failed", "messages": "Failed to enrich contact"},
            )

        for key in ["employment_history", "organization"]:
            person.pop(key)
        person_str = yaml.dump(person, allow_unicode=True)

        apollo_contact = await extract_structure_response(
            person_str,
            ZohoContactData,
            instructions="""
- Unless explicitly asked, only output the value if you are very confident about it, don't make any assumptions.
- Derive the zip code from state and city.
- Mobile number is from Apollo's phone_numbers field which has type "mobile", otherwise phone numbers.
- Mobile and Phone should not be the same.
                """,
        )

        if not apollo_contact:
            langfuse_trace_span.update(output={"error": "Failed to parse enriched contact data"})
            # 解析联系人信息失败
            raise HTTPException(
                status_code=400,
                detail={"error": "failed", "messages": "Failed to parse enriched contact data"},
            )

        # 返回扩充后的联系人数据
        langfuse_trace_span.update(output=apollo_contact)
        return {
            "status": "success",
            "latest_data": apollo_contact.model_dump(),
        }


class QueryZohoContactDataRequest(BaseModel):
    iamadt: str = Field(..., description="The iamadt")
    iambdt: str = Field(..., description="The iambdt")
    csrf_token: str = Field(..., description="The csrf_token")


@router.post("/contact/{contact_id}/data")
async def get_contact_origin_data(contact_id: str, request: QueryZohoContactDataRequest):
    """
    获取联系人原始数据
    """
    try:
        client = ZohoBrowserAPI(request.iamadt, request.iambdt, request.csrf_token)
        contact_data = await client.get_contact_by_id(contact_id)
        if contact_data is None:
            raise HTTPException(status_code=404, detail={"error": "not_found", "message": "Contact not found"})
        logger.info(f"contact_data: {contact_data}")
        return contact_data

    except Exception as e:
        logger.error(f"获取联系人 {contact_id} 原始数据失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail={"error": "internal_error", "message": str(e)})


class UpdateContactDataRequest(BaseModel):
    iamadt: str = Field(..., description="The iamadt")
    iambdt: str = Field(..., description="The iambdt")
    csrf_token: str = Field(..., description="The csrf_token")
    contact: dict = Field(..., description="The contact")


@router.post("/contact/{contact_id}/update")
async def update_contact_data(contact_id: str, request: UpdateContactDataRequest):
    """
    更新联系人数据
    """
    try:
        iamadt = request.iamadt
        iambdt = request.iambdt
        csrf_token = request.csrf_token
        contact = request.contact

        client = ZohoBrowserAPI(iamadt, iambdt, csrf_token)
        logger.info(f"contact_data: {pprint(contact)}")
        contact_data = await client.update_contact_by_id(contact_id, contact)
        if contact_data is None:
            raise HTTPException(status_code=404, detail={"error": "not_found", "message": "Contact not found"})
        logger.info(f"contact_data: {contact_data}")
        return contact_data.get("data", {})[0]

    except Exception as e:
        logger.error(f"更新联系人 {contact_id} 数据失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail={"error": "internal_error", "message": str(e)})
