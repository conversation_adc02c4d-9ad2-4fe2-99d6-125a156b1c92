import asyncio
import sys
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from loguru import logger

from config import (
    SERVER_HOST,
    SERVER_PORT,
)
from server.account.routes import router as account_router
from server.contacts.routes import router as contact_router
from server.contacts.smart_add_contact import router as smart_add_contact_router
from server.emails.routes import router as emails_router
from server.sse_routes import router as sse_router
from server.task.taskiq_app import shutdown, startup
from utils import load_env

# 设置Windows事件循环策略
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())


@asynccontextmanager
async def lifespan(app: FastAPI):
    load_env()
    """FastAPI lifespan manager for handling startup and shutdown events"""
    logger.remove()

    def loguru_formatter(record):
        """
        loguru formatter, add task_id before a message if task_id is in extra
        """
        message = "<level>{message}</level>"
        if record["extra"].get("task_id"):
            message = "<magenta>{extra[task_id]}</magenta> <level>{message}</level>"

        return (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> "
            "<level>{level: <8}</level>"
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - " + message + "\n"
        )

    logger.add(sys.stderr, format=loguru_formatter)

    logger.info("starting taskiq app...")
    await startup()
    logger.info("taskiq app started")

    yield  # Hand over control to FastAPI

    # close
    logger.info("shutting down taskiq app...")
    await shutdown()
    logger.info("taskiq app closed")


def create_app():
    """创建FastAPI应用实例"""
    # 创建FastAPI应用
    app = FastAPI(lifespan=lifespan)

    # 添加中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 包含SSE路由
    app.include_router(sse_router)
    app.include_router(contact_router, prefix="/api/sales-agent")
    app.include_router(account_router, prefix="/api/sales-agent")
    app.include_router(smart_add_contact_router, prefix="/api/sales-agent")
    app.include_router(emails_router, prefix="/api/sales-agent")

    @app.exception_handler(Exception)
    async def handle_exception(request: Request, exc: Exception):
        """全局异常处理"""
        logger.error(f"全局异常处理器: {str(exc)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": str(exc), "detail": "服务器内部错误"},
        )

    return app


app = create_app()


def start():
    """启动服务器"""
    # app = create_app()
    uvicorn.run("server.app:app", host=SERVER_HOST, port=SERVER_PORT, reload=True)


if __name__ == "__main__":
    start()
