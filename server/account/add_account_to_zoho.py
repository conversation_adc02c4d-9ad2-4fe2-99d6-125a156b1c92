from typing import Optional

from langfuse import get_client

from zoho.accounts_api import AccountExistsException, add_account_to_zoho_use_origin_api, get_owner_info_by_user_id


async def add_account_to_crm_zoho(
    account_info: dict,
    user_id: str,
    request_headers: dict = {},
    source_metadata: Optional[dict] = {},
) -> str:
    """
    添加公司到crm zoho

    Args:
        account_info: 公司信息
        user_id: 当前用户ID
        request_headers: 请求头信息
        source_metadata: 来源元数据

    Returns:
        account_id: 添加成功后的公司ID
    """
    langfuse = get_client()
    with langfuse.start_as_current_span(
        name="add_account_to_crm",
        input={"account_info": account_info, "request_headers": request_headers},
    ) as span:
        langfuse.update_current_trace(user_id=user_id, metadata=source_metadata)

        try:
            if not account_info or not user_id:
                raise Exception("account_info or user_id is required")

            owner_info = await get_owner_info_by_user_id(user_id)
            if not owner_info or not owner_info.get("id"):
                raise Exception("Failed to get owner info")

            langfuse.update_current_trace(user_id=owner_info.get("email", user_id))

            account_id = await add_account_to_zoho_use_origin_api(
                account_info=account_info,
                owner_info=owner_info,
                request_headers=request_headers,
            )
            span.update(output={"account_id": account_id})
            return account_id
        except (AccountExistsException, Exception) as e:
            span.update(output={"error": str(e)}, status_message=str(e))
            raise e


if __name__ == "__main__":
    user_id = "*********"
    account_info = {
        "name": "Atlas Copco",
        "website": "https://www.atlascopco.com",
        "account_type": "OEM",
        "industry": "Industry",
        "market_segments": ["Automation", "IoT", "Energy Mgt."],
        "territory": "Sweden",
        "address_state": "Stockholm",
    }
    import asyncio

    result = asyncio.run(add_account_to_crm_zoho(account_info, user_id))
    print(result)
